package com.zhisuo.manager.util;

import java.util.List;

/**
 * 端口工具测试主类
 */
public class PortTestMain {
    public static void main(String[] args) {
        int port = 8081;
        
        System.out.println("=== 端口工具测试 ===");
        
        // 测试端口是否被占用
        boolean inUse = PortUtils.isPortInUse(port);
        System.out.println("端口 " + port + " 是否被占用: " + inUse);
        
        if (inUse) {
            // 获取占用进程
            List<String> pids = PortUtils.getProcessIdsByPort(port);
            System.out.println("占用端口 " + port + " 的进程: " + pids);
            
            // 显示进程信息
            for (String pid : pids) {
                String processInfo = PortUtils.getProcessInfo(pid);
                System.out.println("进程 " + pid + " 信息:");
                System.out.println(processInfo);
            }
            
            // 关闭占用进程
            if (!pids.isEmpty()) {
                int killedCount = PortUtils.killProcessesByPort(port);
                System.out.println("成功关闭 " + killedCount + " 个进程");
                
                // 再次检查端口状态
                boolean stillInUse = PortUtils.isPortInUse(port);
                System.out.println("关闭进程后，端口 " + port + " 是否仍被占用: " + stillInUse);
            }
        } else {
            System.out.println("端口 " + port + " 可用");
        }
        
        System.out.println("=== 测试完成 ===");
    }
}
