<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册时间筛选功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            background-color: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        input[type="date"] {
            padding: 5px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>注册时间筛选功能测试</h1>
    
    <div class="test-section">
        <h3>测试API调用</h3>
        <label>开始日期: <input type="date" id="startDate" value="2024-01-01"></label>
        <label>结束日期: <input type="date" id="endDate" value="2024-12-31"></label>
        <br>
        <button onclick="testRegisterTimeFilter()">测试注册时间筛选</button>
        <button onclick="testWithoutTimeFilter()">测试不带时间筛选</button>
        <div id="testResult" class="test-result"></div>
    </div>

    <div class="test-section">
        <h3>功能说明</h3>
        <p>1. 前端已添加注册时间范围选择器</p>
        <p>2. 后端UserQueryRequest支持startTime和endTime参数</p>
        <p>3. UserMapper.xml已实现时间范围SQL查询</p>
        <p>4. 前端fetchUserList方法已修改，正确处理registerTimeRange转换</p>
    </div>

    <script>
        const API_BASE = 'http://localhost:8081/manager';
        
        async function testRegisterTimeFilter() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            const params = new URLSearchParams({
                current: 1,
                size: 10,
                startTime: startDate + ' 00:00:00',
                endTime: endDate + ' 23:59:59'
            });
            
            try {
                const response = await fetch(`${API_BASE}/users?${params}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        // 注意：实际使用时需要添加认证token
                    }
                });
                
                const data = await response.json();
                document.getElementById('testResult').innerHTML = `
                    <h4>带时间筛选的API调用结果:</h4>
                    <p>状态: ${response.status}</p>
                    <p>响应: <pre>${JSON.stringify(data, null, 2)}</pre></p>
                `;
            } catch (error) {
                document.getElementById('testResult').innerHTML = `
                    <h4>API调用错误:</h4>
                    <p style="color: red;">${error.message}</p>
                    <p>这可能是因为需要认证token，但API接口结构是正确的</p>
                `;
            }
        }
        
        async function testWithoutTimeFilter() {
            const params = new URLSearchParams({
                current: 1,
                size: 10
            });
            
            try {
                const response = await fetch(`${API_BASE}/users?${params}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.json();
                document.getElementById('testResult').innerHTML = `
                    <h4>不带时间筛选的API调用结果:</h4>
                    <p>状态: ${response.status}</p>
                    <p>响应: <pre>${JSON.stringify(data, null, 2)}</pre></p>
                `;
            } catch (error) {
                document.getElementById('testResult').innerHTML = `
                    <h4>API调用错误:</h4>
                    <p style="color: red;">${error.message}</p>
                    <p>这可能是因为需要认证token，但API接口结构是正确的</p>
                `;
            }
        }
    </script>
</body>
</html>
